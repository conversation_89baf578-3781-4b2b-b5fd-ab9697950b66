import asyncio
import logging
import os

# --- LangChain & LangGraph 核心库 ---
from langchain_openai import ChatOpenAI
from langgraph.prebuilt import create_react_agent
from tools import all_base_tools  # 从新的tools包中导入所有基础工具

base_url = "http://124.220.37.159:8301/v1"
api_key = "sk-0eOt5j4XhuBkSn9mBIZigspdqkSHhyvGENO7fEFkYqvcEAfC"
model_name = "glm-4.5"

# 基础日志
logging.basicConfig(level=logging.INFO, format="%(message)s")

# 初始化可流式输出的聊天模型
llm = ChatOpenAI(
    model=model_name,
    openai_api_key=api_key,
    openai_api_base=base_url,
    max_retries=20,
    temperature=0.8,
    streaming=True,  # 开启底层 token 流
    timeout=360,
)


main_agent = create_react_agent(llm, tools=all_base_tools)

for token, metadata in main_agent.stream(
        {"messages": [{"role": "user", "content": "查看当前有哪些文件"}]},
        stream_mode="messages"
):
    print(token, end="", flush=True)
    print("\n")


