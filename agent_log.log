2025-08-19 15:38:40,817 - INFO - ✅ LLM (gemini-2.5-pro) 初始化成功。
2025-08-19 15:38:40,817 - INFO - 🧹 检测到旧的历史记录目录，正在清理 './chroma_db'...
2025-08-19 15:38:40,818 - INFO - ✅ 旧的历史记录已成功清理。
2025-08-19 15:38:41,285 - INFO - ✅ 向量化历史管理器初始化成功，数据将持久化到 './chroma_db' (遥测已禁用)。
2025-08-19 15:38:41,414 - INFO - ✅ 成功加载 3 个MCP工具
2025-08-19 15:38:41,424 - INFO - 
--- 自主智能体已准备就绪 ---
2025-08-19 15:38:46,323 - INFO - HTTP Request: POST http://**************:8301/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-19 15:38:46,368 - INFO - TOKEN_USAGE - Model: gemini-2.5-pro, Prompt: 1317, Completion: 104, Total: 1421
2025-08-19 15:38:46,368 - INFO - 📞 工具调用: NewTask 参数: {
  "task_description": "使用init的工具初始化目录`C:\\Users\\<USER>\\Desktop\\dify-main`项目,并判断项目框架"
}
2025-08-19 15:38:46,368 - INFO - NewTask called with: 使用init的工具初始化目录`C:\Users\<USER>\Desktop\dify-main`项目,并判断项目框架
2025-08-19 15:38:46,369 - INFO - 
📋 [TaskStack] 添加单个任务
2025-08-19 15:38:46,369 - INFO -    详情: 任务: 使用init的工具初始化目录`C:\Users\<USER>\Desktop\dify-main`项目,并判断项目框架
2025-08-19 15:38:46,369 - INFO -    📚 当前任务树 (共 1 个任务):
2025-08-19 15:38:46,369 - INFO - └── 🔄 使用init的工具初始化目录`C:\Users\<USER>\Desktop\dify-main`项目,并判断项目框架 (ID: da2c092a)  <== [正在执行]
2025-08-19 15:38:46,369 - INFO - 
2025-08-19 15:38:46,369 - INFO - 🧠 正在为新任务 '使用init的工具初始化目录`C:\Users\<USER>\Desktop\dify-main`项...' 检索历史经验...
2025-08-19 15:38:46,759 - INFO - HTTP Request: POST https://api.siliconflow.cn/v1/embeddings "HTTP/1.1 200 OK"
2025-08-19 15:38:46,809 - INFO - 📂 未找到相关历史记录。
2025-08-19 15:38:52,736 - INFO - HTTP Request: POST http://**************:8301/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-19 15:38:53,117 - INFO - TOKEN_USAGE - Model: gemini-2.5-pro, Prompt: 10804, Completion: 244, Total: 11048
2025-08-19 15:38:53,117 - INFO - 📞 工具调用: list_directory 参数: {
  "directory_path": "C:\\Users\\<USER>\\Desktop\\dify-main",
  "tree_view": false
}
2025-08-19 15:38:53,119 - INFO - ✨工具输出: list_directory:
结果:
--- Contents of C:\Users\<USER>\Desktop\dify-main ---
d .devcontainer/
- .editorconfig
- .env.example
- .gitattributes
d .github/
- .gitignore
d .vscode/
d api/
- AUTHORS
- CONTRIBUTING.md
- CONTRIBUTING_CN.md
- CONTRIBUTING_DE.md
- CONTRIBUTING_ES.md
- CONTRIBUTING_FR.md
- CONTRIBUTING_JA.md
- CONTRIBUTING_KR.md
- CONTRIBUTING_PT.md
- CONTRIBUTING_TR.md
- CONTRIBUTING_TW.md
- CONTRIBUTING_VI.md
d dev/
d docker/
d images/
- LICENSE
- Makefile
d node_modules/
- package-lock.json
- package.json
- README.md
- README_AR.md
- README_BN.md
- README_CN.md
- README_DE.md
- README_ES.md
- README_FR.md
- README_JA.md
- README_KL.md
- README_KR.md
- README_PT.md
- README_SI.md
- README_TR.md
- README_TW.md
- README_VI.md
d sdks/
d web/
--- End of contents from C:\Users\<USER>\Desktop\dify-main ---
2025-08-19 15:39:01,210 - INFO - HTTP Request: POST http://**************:8301/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-19 15:39:03,863 - INFO - TOKEN_USAGE - Model: gemini-2.5-pro, Prompt: 11247, Completion: 566, Total: 11813
2025-08-19 15:39:03,864 - INFO - 📞 工具调用: list_directory 参数: {
  "directory, tree_view": false
}
2025-08-19 15:39:03,868 - INFO - ✨工具输出: list_directory:
结果:
Error: 1 validation error for ListDirectoryInput
directory_path
  Field required [type=missing, input_value={'directory, tree_view': False}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
 Please fix your mistakes.
2025-08-19 15:39:09,795 - INFO - HTTP Request: POST http://**************:8301/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-19 15:39:11,815 - INFO - TOKEN_USAGE - Model: gemini-2.5-pro, Prompt: 11606, Completion: 285, Total: 11891
2025-08-19 15:39:11,816 - INFO - 📞 工具调用: list_directory 参数: {
  "directory_path": "C:\\Users\\<USER>\\Desktop\\dify-main\\api",
  "tree_view": false
}
2025-08-19 15:39:11,818 - INFO - ✨工具输出: list_directory:
结果:
--- Contents of C:\Users\<USER>\Desktop\dify-main\api ---
- .dockerignore
- .env.example
d .idea/
- .ruff.toml
d .vscode/
- app.py
- app_factory.py
- commands.py
d configs/
d constants/
d contexts/
d controllers/
d core/
- dify_app.py
d docker/
- Dockerfile
d events/
d extensions/
d factories/
d fields/
d libs/
d migrations/
d models/
- mypy.ini
- pyproject.toml
- pytest.ini
- README.md
d repositories/
d schedule/
d services/
d tasks/
d templates/
d tests/
- uv.lock
--- End of contents from C:\Users\<USER>\Desktop\dify-main\api ---
2025-08-19 15:39:13,224 - INFO - 
--- 流程执行完毕，正在清理所有异步资源 ---
2025-08-19 15:39:13,235 - INFO - 
--- Final Token Usage Summary ---
Model: gemini-2.5-pro
Total Prompt Tokens: 34974
Total Completion Tokens: 1199
Total Tokens Used: 36173
---------------------------------
2025-08-19 15:39:13,235 - INFO - 
--- 所有资源已成功清理 ---
